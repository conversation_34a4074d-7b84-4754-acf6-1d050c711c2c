import { auth } from '@/lib/firebase'

export interface GoogleSheet {
  id: string
  name: string
  url: string
  lastModified: string
  sheets: Array<{
    id: number
    title: string
    rowCount: number
    columnCount: number
  }>
}

export interface SheetData {
  data: Record<string, any>[]
  schema: Record<string, string>
  totalRows: number
}

class GoogleSheetsService {
  private async getGoogleAccessToken(): Promise<string | null> {
    try {
      const user = auth.currentUser
      if (!user) {
        throw new Error('User not authenticated')
      }

      // Check if user has Google provider
      const providerData = user.providerData.find(
        provider => provider.providerId === 'google.com'
      )

      if (!providerData) {
        throw new Error('User not authenticated with Google')
      }

      // Check if we have a stored token and if it's still valid
      const accessToken = localStorage.getItem('google_access_token')
      const tokenTimestamp = localStorage.getItem('google_access_token_timestamp')

      if (accessToken && tokenTimestamp) {
        const tokenAge = Date.now() - parseInt(tokenTimestamp)
        const fiftyMinutes = 50 * 60 * 1000 // 50 minutes in milliseconds

        console.log(`🔍 Token age: ${Math.round(tokenAge / 1000 / 60)} minutes (limit: 50 minutes)`)

        if (tokenAge < fiftyMinutes) {
          // Token is still valid, return it
          console.log('✅ Google access token is still valid')
          return accessToken
        } else {
          console.log('⏰ Google access token has expired')
        }
      } else {
        console.log('❌ No Google access token found in localStorage')
      }

      // Token is expired or missing, we need to re-authenticate
      console.log('🔄 Google access token expired or missing, user needs to re-authenticate')
      throw new Error('Google access token has expired. Please sign out and sign in with Google again.')
    } catch (error) {
      console.error('Error getting Google access token:', error)
      return null
    }
  }



  private async makeGoogleAPIRequest(url: string, accessToken?: string, retryCount = 0): Promise<any> {
    // Get access token if not provided
    const token = accessToken || await this.getGoogleAccessToken()
    if (!token) {
      throw new Error('No access token available')
    }

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      let errorMessage = `Google API request failed: ${response.status} ${response.statusText}`

      if (response.status === 401) {
        // Clear the expired token
        console.log('🚫 Google API returned 401 - clearing expired tokens')
        localStorage.removeItem('google_access_token')
        localStorage.removeItem('google_access_token_timestamp')

        errorMessage = 'Google access token has expired. Please sign out and sign in with Google again.'
      } else if (response.status === 403) {
        errorMessage = 'Access denied. Please ensure you have permission to access this Google Sheet.'
      } else if (response.status === 404) {
        errorMessage = 'Google Sheet not found. Please check the URL and try again.'
      }

      console.error('Google API Error:', errorText)
      throw new Error(errorMessage)
    }

    return response.json()
  }

  async getUserSheets(): Promise<GoogleSheet[]> {
    try {
      // Use Google Drive API to list spreadsheets
      const driveApiUrl = 'https://www.googleapis.com/drive/v3/files'
      const params = new URLSearchParams({
        q: "mimeType='application/vnd.google-apps.spreadsheet'",
        fields: 'files(id,name,modifiedTime,webViewLink)',
        orderBy: 'modifiedTime desc',
        pageSize: '50'
      })

      const driveResponse = await this.makeGoogleAPIRequest(
        `${driveApiUrl}?${params}`
      )

      const sheets: GoogleSheet[] = []

      // Just use the basic info from Drive API to avoid rate limiting
      // We'll get detailed sheet info only when a specific sheet is selected
      for (const file of driveResponse.files || []) {
        sheets.push({
          id: file.id,
          name: file.name,
          url: file.webViewLink,
          lastModified: file.modifiedTime,
          sheets: [] // Will be populated when sheet is selected
        })
      }

      return sheets
    } catch (error) {
      console.error('Error fetching user sheets:', error)
      throw new Error('Failed to fetch Google Sheets')
    }
  }

  async getSheetTabs(sheetId: string): Promise<Array<{id: number, title: string, rowCount: number, columnCount: number}>> {
    try {
      // Get spreadsheet metadata using Sheets API
      const sheetsApiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${sheetId}`
      const sheetResponse = await this.makeGoogleAPIRequest(
        `${sheetsApiUrl}?fields=sheets(properties(sheetId,title,gridProperties))`
      )

      return sheetResponse.sheets?.map((sheet: any) => ({
        id: sheet.properties.sheetId,
        title: sheet.properties.title,
        rowCount: sheet.properties.gridProperties?.rowCount || 0,
        columnCount: sheet.properties.gridProperties?.columnCount || 0
      })) || []
    } catch (error) {
      console.error('Error fetching sheet tabs:', error)
      // Return default tab if we can't get details
      return [{ id: 0, title: 'Sheet1', rowCount: 0, columnCount: 0 }]
    }
  }

  async getSheetByUrl(url: string): Promise<GoogleSheet> {
    try {
      // Extract sheet ID from URL
      const sheetIdMatch = url.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/)
      if (!sheetIdMatch) {
        throw new Error('Invalid Google Sheets URL')
      }

      const sheetId = sheetIdMatch[1]

      try {
        // Get file metadata from Drive API
        const driveApiUrl = `https://www.googleapis.com/drive/v3/files/${sheetId}`
        const driveParams = new URLSearchParams({
          fields: 'id,name,modifiedTime,webViewLink'
        })

        const driveResponse = await this.makeGoogleAPIRequest(
          `${driveApiUrl}?${driveParams}`
        )

        // Get sheet tabs using the new method
        const sheetTabs = await this.getSheetTabs(sheetId)

        return {
          id: sheetId,
          name: driveResponse.name,
          url: driveResponse.webViewLink || url,
          lastModified: driveResponse.modifiedTime,
          sheets: sheetTabs
        }
      } catch (apiError) {
        console.error('Error fetching sheet details:', apiError)
        throw new Error('Failed to access the Google Sheet. Please check the URL and ensure you have permission to view it.')
      }
    } catch (error) {
      console.error('Error fetching sheet by URL:', error)
      throw new Error('Failed to fetch Google Sheet')
    }
  }

  async getSheetData(sheetId: string, sheetName: string): Promise<SheetData> {
    try {
      // Use Google Sheets API to fetch data
      const sheetsApiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${sheetId}/values/${encodeURIComponent(sheetName)}`
      const params = new URLSearchParams({
        majorDimension: 'ROWS',
        valueRenderOption: 'UNFORMATTED_VALUE',
        dateTimeRenderOption: 'FORMATTED_STRING'
      })

      const response = await this.makeGoogleAPIRequest(
        `${sheetsApiUrl}?${params}`
      )

      const values = response.values || []

      if (values.length === 0) {
        return {
          data: [],
          schema: {},
          totalRows: 0
        }
      }

      // First row contains headers
      const headers = values[0] || []
      const dataRows = values.slice(1)

      // Infer schema from data
      const schema: Record<string, string> = {}
      headers.forEach((header: string, index: number) => {
        if (!header) return

        // Sample a few values to determine type
        const sampleValues = dataRows
          .slice(0, 10)
          .map(row => row[index])
          .filter(val => val !== null && val !== undefined && val !== '')

        if (sampleValues.length === 0) {
          schema[header] = 'string'
          return
        }

        // Check if all sample values are numbers
        const isNumber = sampleValues.every(val =>
          !isNaN(Number(val)) && isFinite(Number(val))
        )

        // Check if all sample values look like dates
        const isDate = sampleValues.every(val => {
          const dateVal = new Date(val)
          return !isNaN(dateVal.getTime()) &&
                 (val.toString().includes('/') || val.toString().includes('-') || val.toString().includes('T'))
        })

        if (isNumber) {
          schema[header] = 'number'
        } else if (isDate) {
          schema[header] = 'date'
        } else {
          schema[header] = 'string'
        }
      })

      // Convert rows to objects
      const data = dataRows.map(row => {
        const obj: Record<string, any> = {}
        headers.forEach((header: string, index: number) => {
          if (!header) return

          let value = row[index]

          // Convert based on schema
          if (schema[header] === 'number' && value !== null && value !== undefined && value !== '') {
            value = Number(value)
          } else if (schema[header] === 'date' && value) {
            // Keep as string for now, frontend can parse as needed
            value = value.toString()
          } else {
            value = value?.toString() || ''
          }

          obj[header] = value
        })
        return obj
      })

      return {
        data,
        schema,
        totalRows: data.length
      }
    } catch (error) {
      console.error('Error fetching sheet data:', error)
      throw new Error('Failed to fetch sheet data')
    }
  }

  async validateSheetAccess(sheetId: string): Promise<boolean> {
    try {
      // Make a test API call to validate access - use a simple Drive API call
      const driveApiUrl = 'https://www.googleapis.com/drive/v3/about'
      const params = new URLSearchParams({
        fields: 'user'
      })

      await this.makeGoogleAPIRequest(
        `${driveApiUrl}?${params}`
      )

      return true
    } catch (error) {
      console.error('Error validating sheet access:', error)
      return false
    }
  }

  async hasValidGoogleAccess(): Promise<boolean> {
    try {
      const user = auth.currentUser
      if (!user) {
        return false
      }

      // Check if user has Google provider
      const providerData = user.providerData.find(
        provider => provider.providerId === 'google.com'
      )

      if (!providerData) {
        return false
      }

      // Check if we have an access token
      const accessToken = localStorage.getItem('google_access_token')
      if (!accessToken) {
        return false
      }

      // Validate the token with a simple API call
      return await this.validateSheetAccess('test')
    } catch (error) {
      console.error('Error checking Google access:', error)
      return false
    }
  }


}

export const googleSheetsService = new GoogleSheetsService()
